import 'server-only';
import { redirect } from 'next/navigation';
import { getSession, deleteSession } from './session';
import { cache } from 'react';

export const verifySession = cache(async () => {
  const session = await getSession();

  if (!session?.user) {
    redirect('/login');
  }

  return { isAuth: true, user: session.user };
});

export async function isAuthenticated() {
  const session = await getSession();
  return !!session?.user;
}

export async function logout() {
  'use server';
  await deleteSession();
  redirect('/login');
}