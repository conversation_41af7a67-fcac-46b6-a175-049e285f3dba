# Project Memories

## Guideline Details

### App Router
App Router is preferred because it provides better performance and more intuitive routing.

### Server Components
Server Components is preferred to reduce client-side JavaScript and improve initial load times.

## Project History
- Started with Next.js 15.3.5
- Using Google OAuth for authentication

## Command Preferences
- Use direct pnpm commands (e.g., `pnpm dev`) instead of prefixing with `cmd /c`

