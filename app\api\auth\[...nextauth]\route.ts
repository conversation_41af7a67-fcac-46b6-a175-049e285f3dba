import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import { createSession, deleteSession, type SessionUser } from '@/app/lib/session';

// TypeScript interface for Google OAuth profile
interface GoogleProfile {
  sub: string;
  name: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
  email: string;
  email_verified: boolean;
  locale?: string;
}

const googleClientId = process.env.GOOGLE_CLIENT_ID || '';
const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET || '';

if (!googleClientId || !googleClientSecret) {
  throw new Error('Missing Google OAuth credentials');
}

const handler = NextAuth({
  providers: [
    GoogleProvider({
      clientId: googleClientId,
      clientSecret: googleClientSecret,
    }),
  ],
  callbacks: {
    //account, 
    async signIn({ user, profile }) {
      console.log('SIGNIN CALLBACK profile:', profile);

      // Create our custom session when user signs in
      if (user && profile) {
        // Type-safe access to Google OAuth profile
        const googleProfile = profile as GoogleProfile;
        const sessionUser: SessionUser = {
          id: user.id,
          name: user.name || '',
          email: user.email || '',
          image: user.image || '',
          picture: googleProfile.picture || user.image || '',
          given_name: googleProfile.given_name,
          family_name: googleProfile.family_name,
        };
        await createSession(sessionUser);
      }

      return true;
    },
    async session({ session }) {
      return session;
    },
  },
  events: {
    async signOut() {
      // Delete our custom session when user signs out
      await deleteSession();
    },
  },
});

export { handler as GET, handler as POST };
