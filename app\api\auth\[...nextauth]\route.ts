import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import { createSession, deleteSession, type SessionUser } from '@/app/lib/session';

// TypeScript interface for Google OAuth profile
interface GoogleProfile {
  sub: string;
  name: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
  email: string;
  email_verified: boolean;
  locale?: string;
}

// TypeScript interface for GitHub OAuth profile
interface GitHubProfile {
  id: number;
  login: string;
  name: string | null;
  email: string | null;
  avatar_url: string;
  bio?: string | null;
  location?: string | null;
  company?: string | null;
}

const googleClientId = process.env.GOOGLE_CLIENT_ID || '';
const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET || '';
const githubClientId = process.env.GITHUB_ID || '';
const githubClientSecret = process.env.GITHUB_SECRET || '';

if (!googleClientId || !googleClientSecret) {
  throw new Error('Missing Google OAuth credentials');
}

if (!githubClientId || !githubClientSecret) {
  throw new Error('Missing GitHub OAuth credentials');
}

const handler = NextAuth({
  providers: [
    GoogleProvider({
      clientId: googleClientId,
      clientSecret: googleClientSecret,
    }),
    GitHubProvider({
      clientId: githubClientId,
      clientSecret: githubClientSecret,
    }),
  ],
  callbacks: {
    //account,
    async signIn({ user, profile, account }) {
      console.log('SIGNIN CALLBACK profile:', profile);
      console.log('SIGNIN CALLBACK account:', account);

      // Create our custom session when user signs in
      if (user && profile && account) {
        let sessionUser: SessionUser;

        if (account.provider === 'google') {
          // Type-safe access to Google OAuth profile
          const googleProfile = profile as GoogleProfile;
          sessionUser = {
            id: user.id,
            name: user.name || '',
            email: user.email || '',
            image: user.image || '',
            picture: googleProfile.picture || user.image || '',
            given_name: googleProfile.given_name,
            family_name: googleProfile.family_name,
          };
        } else if (account.provider === 'github') {
          // Type-safe access to GitHub OAuth profile
          const githubProfile = profile as GitHubProfile;
          // GitHub doesn't provide given_name/family_name, so we'll parse the name if available
          const nameParts = githubProfile.name?.split(' ') || [];
          sessionUser = {
            id: user.id,
            name: user.name || githubProfile.name || githubProfile.login,
            email: user.email || githubProfile.email || '',
            image: user.image || githubProfile.avatar_url || '',
            picture: githubProfile.avatar_url || user.image || '',
            given_name: nameParts[0] || undefined,
            family_name: nameParts.slice(1).join(' ') || undefined,
          };
        } else {
          // Fallback for other providers
          sessionUser = {
            id: user.id,
            name: user.name || '',
            email: user.email || '',
            image: user.image || '',
            picture: user.image || '',
          };
        }

        await createSession(sessionUser);
      }

      return true;
    },
    async session({ session }) {
      return session;
    },
  },
  events: {
    async signOut() {
      // Delete our custom session when user signs out
      await deleteSession();
    },
  },
});

export { handler as GET, handler as POST };
