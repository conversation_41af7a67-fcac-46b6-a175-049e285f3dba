import { verifySession, logout } from '@/app/lib/auth';
import Image from 'next/image';
import styles from './LogoutButton.module.css';

export default async function DashboardPage() {
  // Use try/catch for better error handling
  try {
    const { user } = await verifySession();

    return (
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Dashboard</h1>
              <form action={logout}>
                <button
                  type="submit"
                  data-logout="true"
                  className={`${styles.logoutButton} logout-button bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 text-white dark:text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-red-400 border-0 cursor-pointer`}
                >
                  Logout
                </button>
              </form>
            </div>
            <p className="mb-4">Welcome, {user.name}!</p>
            <div className="bg-gray-50 p-4 rounded-md">
              <h2 className="text-lg font-medium mb-2">Your Profile</h2>
              <ul className="space-y-2">
                <li><strong>Name:</strong> {user.name}</li>
                <li><strong>Email:</strong> {user.email}</li>
                {user.given_name && (
                  <li><strong>Given Name:</strong> {user.given_name}</li>
                )}
                {user.family_name && (
                  <li><strong>Family Name:</strong> {user.family_name}</li>
                )}
                {user.picture && (
                  <li className="flex items-center space-x-2">
                    <strong>Picture:</strong>
                    <Image
                      src={user.picture}
                      alt={`Profile picture of ${user.name || 'User'}`}
                      width={64}
                      height={64}
                      className="rounded-full"
                    />
                  </li>
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    // This shouldn't normally happen due to middleware protection
    // but provides a fallback just in case
    console.error('Error in DashboardPage:', error);
    return (
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <p>Error loading dashboard. Please try signing in again.</p>
      </div>
    );
  }
}
